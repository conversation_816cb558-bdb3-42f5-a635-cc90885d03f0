#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为 us_pmn 数据库添加字段配置
 */

// us_pmn 字段配置定义
const US_PMN_FIELD_CONFIGS = [
  {
    fieldName: 'id',
    displayName: 'ID',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 1,
    searchType: 'exact' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'knumber',
    displayName: 'K Number',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    sortOrder: 1,
    listOrder: 1,
    detailOrder: 2,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'applicant',
    displayName: 'Applicant',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    sortOrder: 2,
    listOrder: 2,
    detailOrder: 3,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'contact',
    displayName: 'Contact',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: true,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 4,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'street1',
    displayName: 'Street Address 1',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 5,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'street2',
    displayName: 'Street Address 2',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 6,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'city',
    displayName: 'City',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: true,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 7,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'state',
    displayName: 'State',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: true,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 8,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'country_code',
    displayName: 'Country Code',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 9,
    searchType: 'exact' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'zip',
    displayName: 'ZIP Code',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 10,
    searchType: 'exact' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'postal_code',
    displayName: 'Postal Code',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 11,
    searchType: 'exact' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'datereceived',
    displayName: 'Date Received',
    fieldType: 'date' as const,
    isVisible: true,
    isSearchable: false,
    isFilterable: true,
    isSortable: true,
    sortOrder: 3,
    listOrder: 3,
    detailOrder: 12,
    searchType: 'date_range' as const,
    filterType: 'date_range' as const
  },
  {
    fieldName: 'decisiondate',
    displayName: 'Decision Date',
    fieldType: 'date' as const,
    isVisible: true,
    isSearchable: false,
    isFilterable: true,
    isSortable: true,
    sortOrder: 4,
    listOrder: 4,
    detailOrder: 13,
    searchType: 'date_range' as const,
    filterType: 'date_range' as const
  },
  {
    fieldName: 'decision',
    displayName: 'Decision',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    sortOrder: 5,
    listOrder: 5,
    detailOrder: 14,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'reviewadvisecomm',
    displayName: 'Review Advisory Committee',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: true,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 15,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'productcode',
    displayName: 'Product Code',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    sortOrder: 6,
    listOrder: 6,
    detailOrder: 16,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'stateorsumm',
    displayName: 'Statement or Summary',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: true,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 17,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'classadvisecomm',
    displayName: 'Classification Advisory Committee',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: true,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 18,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'sspindicator',
    displayName: 'SSP Indicator',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 19,
    searchType: 'exact' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'type',
    displayName: 'Type',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    sortOrder: 7,
    listOrder: 7,
    detailOrder: 20,
    searchType: 'contains' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'thirdparty',
    displayName: 'Third Party',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 21,
    searchType: 'exact' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'expeditedreview',
    displayName: 'Expedited Review',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: true,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 22,
    searchType: 'exact' as const,
    filterType: 'select' as const
  },
  {
    fieldName: 'devicename',
    displayName: 'Device Name',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    sortOrder: 8,
    listOrder: 8,
    detailOrder: 23,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'source_file',
    displayName: 'Source File',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 24,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'source_time',
    displayName: 'Source Time',
    fieldType: 'text' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: false,
    isSortable: false,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 25,
    searchType: 'contains' as const,
    filterType: 'input' as const
  },
  {
    fieldName: 'decision_year',
    displayName: 'Decision Year',
    fieldType: 'number' as const,
    isVisible: false,
    isSearchable: false,
    isFilterable: true,
    isSortable: true,
    sortOrder: 0,
    listOrder: 0,
    detailOrder: 26,
    searchType: 'range' as const,
    filterType: 'range' as const
  }
];

async function addUsPmnFieldConfigs() {
  console.log('🔧 为 us_pmn 数据库添加字段配置...');

  try {
    // 检查是否已存在配置
    const existingConfigs = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_pmn' },
      select: { fieldName: true }
    });

    const existingFieldNames = existingConfigs.map(config => config.fieldName);
    console.log(`📋 已存在 ${existingFieldNames.length} 个字段配置: ${existingFieldNames.join(', ')}`);

    // 过滤出需要添加的字段
    const fieldsToAdd = US_PMN_FIELD_CONFIGS.filter(
      config => !existingFieldNames.includes(config.fieldName)
    );

    if (fieldsToAdd.length === 0) {
      console.log('✅ 所有字段配置已存在，无需添加');
      return;
    }

    console.log(`🚀 准备添加 ${fieldsToAdd.length} 个新字段配置...`);

    // 批量添加字段配置
    const results = await Promise.all(
      fieldsToAdd.map(async (config) => {
        try {
          const result = await db.fieldConfig.create({
            data: {
              databaseCode: 'us_pmn',
              ...config
            },
            select: { fieldName: true, displayName: true }
          });
          console.log(`  ✅ 添加字段: ${result.fieldName} (${result.displayName})`);
          return result;
        } catch (error) {
          console.error(`  ❌ 添加字段失败 ${config.fieldName}:`, error);
          return null;
        }
      })
    );

    const successCount = results.filter(r => r !== null).length;
    console.log(`\n✅ 成功添加 ${successCount} 个字段配置`);

    // 验证最终结果
    const finalCount = await db.fieldConfig.count({
      where: { databaseCode: 'us_pmn', isActive: true }
    });
    console.log(`📊 us_pmn 数据库现有 ${finalCount} 个激活的字段配置`);

  } catch (error) {
    console.error('❌ 添加字段配置失败:', error);
  } finally {
    await db.$disconnect();
  }
}

addUsPmnFieldConfigs();
