#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 修复 us_pmn 数据库中 devicename 字段的筛选配置
 * 问题：devicename 字段 isFilterable: false，导致在 Filter 面板中不显示
 * 解决：将 isFilterable 设为 true，同时确保 isAdvancedSearchable 也为 true
 */

async function fixDeviceNameFilter() {
  console.log('🔧 修复 us_pmn 数据库中 devicename 字段的筛选配置...\n');

  try {
    // 1. 检查当前配置
    console.log('📋 1. 检查当前 devicename 字段配置...');
    const currentConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'devicename',
        isActive: true
      },
      select: {
        id: true,
        fieldName: true,
        displayName: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        isSearchable: true,
        filterType: true,
        searchType: true
      }
    });

    if (!currentConfig) {
      console.error('❌ 未找到 devicename 字段配置');
      return;
    }

    console.log('当前配置:');
    console.table([currentConfig]);

    // 2. 更新配置
    console.log('\n🚀 2. 更新 devicename 字段配置...');
    const updatedConfig = await db.fieldConfig.update({
      where: {
        id: currentConfig.id
      },
      data: {
        isFilterable: true,
        isAdvancedSearchable: true,
        filterType: 'input',
        searchType: 'contains'
      },
      select: {
        id: true,
        fieldName: true,
        displayName: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        isSearchable: true,
        filterType: true,
        searchType: true
      }
    });

    console.log('更新后配置:');
    console.table([updatedConfig]);

    // 3. 清除缓存
    console.log('\n🧹 3. 清除配置缓存...');
    try {
      // 尝试清除缓存（如果有缓存服务的话）
      const { ConfigCacheService } = await import('../src/lib/services/configCacheService');
      await ConfigCacheService.clearDatabaseCache('us_pmn');
      await ConfigCacheService.clearAllCache();
      console.log('✅ 缓存已清除');
    } catch (error) {
      console.log('⚠️  缓存清除失败（可能没有缓存服务）:', error.message);
    }

    // 4. 验证配置
    console.log('\n🔍 4. 验证修复结果...');
    
    // 检查数据库配置
    const verifyConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'devicename',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        filterType: true,
        searchType: true
      }
    });

    console.log('验证数据库配置:');
    console.table([verifyConfig]);

    // 检查API配置
    try {
      console.log('\n🌐 5. 测试API配置...');
      const response = await fetch('http://localhost:3000/api/meta/us_pmn');
      const result = await response.json();
      
      if (result.success) {
        const apiField = result.config.fields.find((f: any) => f.fieldName === 'devicename');
        console.log('API 返回的 devicename 配置:');
        console.table([{
          fieldName: apiField?.fieldName,
          displayName: apiField?.displayName,
          isFilterable: apiField?.isFilterable,
          isAdvancedSearchable: apiField?.isAdvancedSearchable,
          filterType: apiField?.filterType,
          searchType: apiField?.searchType
        }]);
        
        // 验证结果
        if (apiField?.isFilterable && apiField?.isAdvancedSearchable) {
          console.log('\n✅ 修复成功！');
          console.log('🎯 现在请在浏览器中:');
          console.log('   1. 访问 http://localhost:3000/data/list/us_pmn');
          console.log('   2. 强制刷新页面 (Ctrl+F5 或 Cmd+Shift+R)');
          console.log('   3. 检查筛选器面板中是否显示 "Device Name" 输入框');
          console.log('   4. 测试在 Filter 面板中搜索 device name');
          console.log('   5. 验证搜索结果与 Advanced Search 一致');
        } else {
          console.log('\n❌ 修复失败，API 返回的配置仍不正确');
          console.log('请检查缓存是否完全清除，或重启应用服务器');
        }
      } else {
        console.error('❌ API 测试失败:', result.error);
      }
    } catch (error) {
      console.error('❌ API 测试失败:', error.message);
      console.log('请确保应用服务器正在运行 (npm run dev)');
    }

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
  }
}

// 运行修复脚本
if (require.main === module) {
  fixDeviceNameFilter()
    .then(() => {
      console.log('\n🎉 修复脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 修复脚本执行失败:', error);
      process.exit(1);
    });
}

export { fixDeviceNameFilter };
