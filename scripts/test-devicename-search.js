#!/usr/bin/env node

/**
 * 测试 us_pmn 数据库中 devicename 字段的搜索功能
 * 验证 Filter 面板和 Advanced Search 的搜索结果是否一致
 */

// 使用内置的 fetch API (Node.js 18+)
const fetch = globalThis.fetch;

async function testDeviceNameSearch() {
  console.log('🔍 测试 us_pmn 数据库中 devicename 字段的搜索功能...\n');

  const baseUrl = 'http://localhost:3000';
  const testKeyword = 'dental'; // 使用已知有结果的关键词
  
  try {
    // 1. 测试 Advanced Search API (Drizzle)
    console.log('1. 测试 Advanced Search API (通过 devicename 字段搜索)');
    const advancedSearchResponse = await fetch(`${baseUrl}/api/drizzle-search/us_pmn`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        conditions: [{
          id: 'test_1',
          field: 'devicename',
          value: testKeyword,
          logic: undefined
        }],
        page: 1,
        limit: 10
      })
    });
    
    const advancedSearchData = await advancedSearchResponse.json();
    console.log(`   状态: ${advancedSearchData.success ? '✅' : '❌'}`);
    console.log(`   数据条数: ${advancedSearchData.data?.length || 0}`);
    console.log(`   总记录数: ${advancedSearchData.pagination?.total || 0}`);
    
    if (advancedSearchData.error) {
      console.log(`   错误: ${advancedSearchData.error}`);
    }
    console.log('');

    // 2. 测试统一搜索API (模拟Filter面板)
    console.log('2. 测试统一搜索API (模拟 Filter 面板搜索)');
    const unifiedSearchResponse = await fetch(`${baseUrl}/api/unified-database-search/us_pmn`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        filters: {
          devicename: testKeyword
        },
        page: 1,
        limit: 10
      })
    });
    
    const unifiedSearchData = await unifiedSearchResponse.json();
    console.log(`   状态: ${unifiedSearchData.success ? '✅' : '❌'}`);
    console.log(`   数据条数: ${unifiedSearchData.data?.length || 0}`);
    console.log(`   总记录数: ${unifiedSearchData.pagination?.total_results || unifiedSearchData.pagination?.total || 0}`);
    
    if (unifiedSearchData.error) {
      console.log(`   错误: ${unifiedSearchData.error}`);
    }
    console.log('');

    // 3. 比较结果
    console.log('3. 比较搜索结果');
    const advancedTotal = advancedSearchData.pagination?.total || 0;
    const unifiedTotal = unifiedSearchData.pagination?.total_results || unifiedSearchData.pagination?.total || 0;
    
    console.log(`   Advanced Search 总数: ${advancedTotal}`);
    console.log(`   Filter Panel 总数: ${unifiedTotal}`);
    
    if (advancedTotal === unifiedTotal && advancedTotal > 0) {
      console.log('   ✅ 搜索结果一致！Filter 面板和 Advanced Search 返回相同数量的结果');
    } else if (advancedTotal === unifiedTotal && advancedTotal === 0) {
      console.log('   ⚠️  两个搜索都没有返回结果，可能关键词不匹配或数据问题');
    } else {
      console.log('   ❌ 搜索结果不一致！需要进一步调试');
    }
    console.log('');

    // 4. 测试配置API
    console.log('4. 验证字段配置');
    const configResponse = await fetch(`${baseUrl}/api/meta/us_pmn`);
    const configData = await configResponse.json();
    
    if (configData.success) {
      const deviceNameField = configData.config.fields.find(f => f.fieldName === 'devicename');
      if (deviceNameField) {
        console.log('   Device Name 字段配置:');
        console.log(`     - isFilterable: ${deviceNameField.isFilterable ? '✅' : '❌'}`);
        console.log(`     - isAdvancedSearchable: ${deviceNameField.isAdvancedSearchable ? '✅' : '❌'}`);
        console.log(`     - filterType: ${deviceNameField.filterType}`);
        console.log(`     - searchType: ${deviceNameField.searchType}`);
        
        if (deviceNameField.isFilterable && deviceNameField.isAdvancedSearchable) {
          console.log('   ✅ 字段配置正确');
        } else {
          console.log('   ❌ 字段配置有问题');
        }
      } else {
        console.log('   ❌ 未找到 devicename 字段配置');
      }
    } else {
      console.log(`   ❌ 配置API失败: ${configData.error}`);
    }
    console.log('');

    // 5. 总结
    console.log('📊 测试总结:');
    if (advancedSearchData.success && unifiedSearchData.success && advancedTotal === unifiedTotal && advancedTotal > 0) {
      console.log('✅ 修复成功！Device Name 字段在 Filter 面板和 Advanced Search 中都能正常工作');
      console.log('🎯 建议测试步骤:');
      console.log('   1. 打开 http://localhost:3000/data/list/us_pmn');
      console.log('   2. 在 Filter 面板的 Device Name 输入框中输入 "dental"');
      console.log('   3. 点击 Apply 按钮');
      console.log('   4. 验证是否显示搜索结果');
      console.log('   5. 在 Advanced Search 中添加相同条件，验证结果一致');
    } else {
      console.log('❌ 仍有问题需要解决');
      console.log('🔧 建议检查:');
      console.log('   1. 确保应用服务器正在运行');
      console.log('   2. 清除浏览器缓存');
      console.log('   3. 检查数据库连接');
      console.log('   4. 查看服务器日志');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testDeviceNameSearch().catch(console.error);
