import { type NextRequest, NextResponse } from 'next/server';
import { validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { DrizzleSearchService } from '@/lib/services/drizzleSearchService';

export const dynamic = 'force-dynamic';

/**
 * GET /api/unified-database-search/[database]?q=<keyword>&page=1&limit=20&filters={}
 * 统一数据库搜索API - 兼容Filter面板的搜索请求
 * 使用Drizzle搜索服务
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const searchParams = request.nextUrl.searchParams;
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error || `不支持的数据库代码: ${database}`
      }, { status: validation.status || 400 });
    }
    
    // 解析查询参数
    const query = searchParams.get('q')?.trim() || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || undefined;
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';
    
    // 解析筛选条件
    let filters = {};
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        filters = JSON.parse(filtersParam);
      } catch (error) {
        console.warn('[UnifiedSearch API] 筛选条件解析失败:', error);
      }
    }
    
    // 处理allFields参数（兼容现有逻辑）
    const allFields = searchParams.get('allFields');
    if (allFields) {
      filters = { ...filters, allFields };
    }
    
    console.log('[UnifiedSearch API] GET请求:', { 
      database, 
      query: query || allFields, 
      page, 
      limit, 
      sortBy, 
      sortOrder,
      filters 
    });
    
    // 将filters转换为conditions格式
    const conditions = convertFiltersToConditions(filters);
    
    // 使用Drizzle搜索服务
    const result = await DrizzleSearchService.search({
      database,
      conditions,
      globalKeyword: query || allFields,
      page,
      limit,
      sortBy,
      sortOrder
    });
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.pagination?.page || page,
        limit: result.pagination?.limit || limit,
        total: result.pagination?.total || 0,
        total_results: result.pagination?.total || 0, // 兼容字段
        totalPages: result.pagination?.totalPages || 0
      },
      search_info: result.searchInfo
    });
    
  } catch (error) {
    console.error('[UnifiedSearch API] GET错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索失败',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, total_results: 0, totalPages: 0 }
    }, { status: 500 });
  }
}

/**
 * POST /api/unified-database-search/[database]
 * 支持更复杂的数据库搜索参数 - 主要用于Filter面板
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error || `不支持的数据库代码: ${database}`
      }, { status: validation.status || 400 });
    }
    
    const body = await request.json();
    const { 
      query = '', 
      filters = {}, 
      sortBy, 
      sortOrder = 'desc',
      page = 1, 
      limit = 20 
    } = body;
    
    console.log('[UnifiedSearch API] POST请求:', { 
      database, 
      query, 
      page, 
      limit, 
      sortBy, 
      sortOrder,
      filters 
    });
    
    // 将filters转换为conditions格式
    const conditions = convertFiltersToConditions(filters);
    
    // 使用Drizzle搜索服务
    const result = await DrizzleSearchService.search({
      database,
      conditions,
      globalKeyword: query,
      page,
      limit,
      sortBy,
      sortOrder
    });
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.pagination?.page || page,
        limit: result.pagination?.limit || limit,
        total: result.pagination?.total || 0,
        total_results: result.pagination?.total || 0, // 兼容字段
        totalPages: result.pagination?.totalPages || 0
      },
      search_info: result.searchInfo
    });
    
  } catch (error) {
    console.error('[UnifiedSearch API] POST错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索失败',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, total_results: 0, totalPages: 0 }
    }, { status: 500 });
  }
}

/**
 * 将Filter参数转换为SearchCondition格式
 */
function convertFiltersToConditions(filters: Record<string, any>) {
  const conditions: Array<{
    id: string;
    field: string;
    value: any;
    logic?: 'AND' | 'OR' | 'NOT';
  }> = [];

  Object.entries(filters).forEach(([fieldName, value], index) => {
    if (value === undefined || value === null || value === '') return;
    
    // 跳过特殊字段
    if (fieldName === 'allFields') return;

    const condition = {
      id: `filter_${fieldName}_${Date.now()}_${index}`,
      field: fieldName,
      value,
      logic: index > 0 ? 'AND' as const : undefined,
    };

    conditions.push(condition);
  });

  return conditions;
}
