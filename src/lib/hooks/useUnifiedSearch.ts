import { useState, useCallback } from 'react';
import { SearchCondition } from '@/lib/api';

// 客户端版本的字段配置接口，避免引用服务器端模块
export interface DatabaseFieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json';
  isVisible: boolean;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable: boolean;
  isSortable: boolean;
  isStatisticsEnabled: boolean;
  filterType: 'select' | 'input' | 'date_range' | 'checkbox' | 'multi_select' | 'range';
  searchType: 'exact' | 'contains' | 'range' | 'date_range' | 'starts_with' | 'ends_with';
}

// 统一搜索状态接口
export interface UnifiedSearchState {
  database: string;
  conditions: SearchCondition[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
  sorting: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  };
  globalKeyword?: string;
  
  // 统计数据缓存
  statisticsCache: Map<string, FieldStatistics>;
  
  // 加载状态
  isLoading: boolean;
  isStatisticsLoading: boolean;
}

// 字段统计数据接口
export interface FieldStatistics {
  fieldName: string;
  items: Array<{
    value: string;
    count: number;
  }>;
  total: number;
  lastUpdated: number;
}

// 统一搜索参数接口
export interface UnifiedSearchParams {
  database: string;
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  globalKeyword?: string;
}

/**
 * 统一搜索Hook - 管理Filter面板和Advanced Search的共享状态
 */
export const useUnifiedSearch = (database: string, config?: { fields: DatabaseFieldConfig[] }) => {
  const [searchState, setSearchState] = useState<UnifiedSearchState>({
    database,
    conditions: [],
    pagination: { page: 1, limit: 20, total: 0 },
    sorting: {},
    statisticsCache: new Map(),
    isLoading: false,
    isStatisticsLoading: false,
  });

  // 执行搜索
  const executeSearch = useCallback(async (params?: Partial<UnifiedSearchParams>) => {
    if (!config) return;

    setSearchState(prev => ({ ...prev, isLoading: true }));

    try {
      const searchParams: UnifiedSearchParams = {
        database,
        conditions: params?.conditions !== undefined ? params.conditions : searchState.conditions,
        page: params?.page !== undefined ? params.page : searchState.pagination.page,
        limit: params?.limit !== undefined ? params.limit : searchState.pagination.limit,
        sortBy: params?.sortBy !== undefined ? params.sortBy : searchState.sorting.sortBy,
        sortOrder: params?.sortOrder !== undefined ? params.sortOrder : searchState.sorting.sortOrder,
        globalKeyword: params?.globalKeyword !== undefined ? params.globalKeyword : searchState.globalKeyword,
      };

      // 调用新的Drizzle搜索API
      const response = await fetch(`/api/drizzle-search/${database}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conditions: searchParams.conditions,
          globalKeyword: searchParams.globalKeyword,
          page: searchParams.page,
          limit: searchParams.limit,
          sortBy: searchParams.sortBy,
          sortOrder: searchParams.sortOrder
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSearchState(prev => ({
          ...prev,
          pagination: {
            page: result.pagination?.page || searchParams.page || 1,
            limit: result.pagination?.limit || searchParams.limit || 20,
            total: result.pagination?.total || 0,
          },
          conditions: searchParams.conditions,
          sorting: {
            sortBy: searchParams.sortBy,
            sortOrder: searchParams.sortOrder,
          },
          globalKeyword: searchParams.globalKeyword,
          isLoading: false,
        }));

        // 触发统计数据更新
        if (searchParams.conditions.length > 0) {
          updateStatisticsCache(searchParams.conditions);
        }
      } else {
        setSearchState(prev => ({ ...prev, isLoading: false }));
      }

      return result;
    } catch (error) {
      console.error('搜索执行失败:', error);
      setSearchState(prev => ({ ...prev, isLoading: false }));
      throw error;
    }
  }, [database, config]);

  // 更新统计缓存
  const updateStatisticsCache = useCallback(async (conditions: SearchCondition[]) => {
    if (!config) return;

    setSearchState(prev => ({ ...prev, isStatisticsLoading: true }));

    try {
      const statisticsFields = config.fields.filter(f => f.isStatisticsEnabled);

      // 并行获取所有字段的统计数据 - 使用新的Drizzle API
      const statisticsPromises = statisticsFields.map(async (field) => {
        try {
          const response = await fetch(`/api/drizzle-statistics/${database}/${field.fieldName}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              conditions,
              limit: field.statisticsMaxLimit || 50,
            }),
          });

          const result = await response.json();

          if (result.success) {
            return {
              fieldName: field.fieldName,
              statistics: {
                fieldName: field.fieldName,
                items: result.data.items || [],
                total: result.data.total || 0,
                lastUpdated: Date.now(),
              }
            };
          }
        } catch (error) {
          console.error(`获取字段 ${field.fieldName} 统计失败:`, error);
        }
        return null;
      });

      const results = await Promise.all(statisticsPromises);

      setSearchState(prev => {
        const newCache = new Map(prev.statisticsCache);
        results.forEach(result => {
          if (result) {
            newCache.set(result.fieldName, result.statistics);
          }
        });

        return {
          ...prev,
          statisticsCache: newCache,
          isStatisticsLoading: false,
        };
      });
    } catch (error) {
      console.error('统计缓存更新失败:', error);
      setSearchState(prev => ({ ...prev, isStatisticsLoading: false }));
    }
  }, [database, config]);

  // Filter面板更新条件（基于isFilterable字段）
  const updateFromFilterPanel = useCallback(async (filters: Record<string, any>) => {
    if (!config) return;

    const filterableFields = config.fields.filter(f => f.isFilterable);
    const conditions = convertFiltersToConditions(filters, filterableFields);

    // 执行搜索并更新状态
    return await executeSearch({ conditions, page: 1 });
  }, [config, executeSearch]);

  // Advanced Search更新条件（基于isAdvancedSearchable字段）
  const updateFromAdvancedSearch = useCallback(async (conditions: SearchCondition[]) => {
    // 执行搜索并更新状态
    return await executeSearch({ conditions, page: 1 });
  }, [executeSearch]);

  // 获取字段统计数据
  const getFieldStatistics = useCallback((fieldName: string): FieldStatistics | undefined => {
    return searchState.statisticsCache.get(fieldName);
  }, [searchState.statisticsCache]);

  // 更新排序
  const updateSorting = useCallback(async (sortBy: string, sortOrder: 'asc' | 'desc') => {
    return await executeSearch({ sortBy, sortOrder });
  }, [executeSearch]);

  // 更新分页
  const updatePagination = useCallback(async (page: number, limit?: number) => {
    const newLimit = limit || searchState.pagination.limit;
    return await executeSearch({ page, limit: newLimit });
  }, [executeSearch, searchState.pagination.limit]);

  // 清空所有条件
  const clearAllConditions = useCallback(async () => {
    return await executeSearch({ conditions: [], page: 1 });
  }, [executeSearch]);

  return {
    searchState,
    executeSearch,
    updateStatisticsCache,
    updateFromFilterPanel,
    updateFromAdvancedSearch,
    getFieldStatistics,
    updateSorting,
    updatePagination,
    clearAllConditions,
  };
};

/**
 * 将Filter参数转换为SearchCondition格式
 */
function convertFiltersToConditions(
  filters: Record<string, any>,
  filterableFields: DatabaseFieldConfig[]
): SearchCondition[] {
  const conditions: SearchCondition[] = [];

  Object.entries(filters).forEach(([fieldName, value], index) => {
    if (value === undefined || value === null || value === '') return;

    const fieldConfig = filterableFields.find(f => f.fieldName === fieldName);
    if (!fieldConfig) return;

    const condition: SearchCondition = {
      id: `filter_${fieldName}_${Date.now()}`,
      field: fieldName,
      value,
      logic: index > 0 ? 'AND' : undefined,
    };

    conditions.push(condition);
  });

  return conditions;
}
